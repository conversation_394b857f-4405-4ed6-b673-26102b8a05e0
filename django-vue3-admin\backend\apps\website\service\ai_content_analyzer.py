"""
AI内容分析服务
专门处理URL的HTML抓取、内容分析和AI分类逻辑
用于03_update_content_type_by_ai.py和03_update_content_type_by_ai_concurrent.py的共同逻辑
"""

import logging
from typing import Dict, Any
from django.db.models import Q

from ..models import SiteMap
from .ai_service import ai_service
from .html_parser import HtmlParser
from .site_map_service import SiteMapService
from .content_type_matcher import ContentTypeMatcher

logger = logging.getLogger(__name__)


class AIContentAnalyzer:
    """AI内容分析器 - 处理URL的完整分析流程"""
    
    def __init__(self, site):
        """
        初始化AI内容分析器
        
        Args:
            site: 站点对象
        """
        self.site = site
        self.html_parser = HtmlParser()
        self.site_map_service = SiteMapService()
        
        # 初始化内容类型匹配器
        self.content_type_matcher = None
        if hasattr(site, 'url_matching_rules') and site.url_matching_rules:
            self.content_type_matcher = ContentTypeMatcher(site.url_matching_rules)
    
    def analyze_url_with_html_prefetch(self, url: str) -> Dict[str, Any]:
        """
        分析URL内容类型（重构版：通过SiteMap获取HTML）
        
        这个方法会：
        1. 获取或创建SiteMap记录
        2. 通过SiteMap获取HTML内容（利用缓存机制）
        3. 如果需要，发现新链接并添加到站点地图
        4. 对新链接应用规则匹配
        5. 将URL和HTML内容一起提交给AI分析
        
        Args:
            url: 要分析的URL
            
        Returns:
            分析结果字典
        """
        try:
            logger.info(f"开始分析URL: {url}")
            
            # 1. 获取或创建SiteMap记录
            sitemap = self.site_map_service.create_or_update_sitemap_by_url(
                self.site, url
            )
            
            # 2. 通过SiteMap获取HTML内容（利用缓存机制）
            html_content = sitemap.get_html()
            if not html_content:
                return {'success': False, 'error': 'HTML内容不可用', 'url': url}
            
            # 3. 提取页面信息并更新标题
            page_info = self.html_parser.extract_page_info(html_content)
            title = page_info.get('title', '')
            if title and sitemap.title != title:
                sitemap.title = title
                sitemap.save(update_fields=['title'])
                logger.info(f"已更新URL标题: {title}")
            
            # 4. 提取链接并添加到站点地图
            new_links_count = 0
            links = self.html_parser.extract_links(html_content, url)
            
            if links:
                # 筛选同站链接
                internal_links = self.html_parser.filter_internal_links(links, self.site.domain)
                
                # 批量创建站点地图条目
                for link in internal_links:
                    link_url = link['url']
                    link_title = link['text'] or link['title']
                    
                    self.site_map_service.create_or_update_sitemap_by_url(
                        self.site, link_url, title=link_title
                    )
                    new_links_count += 1
                
                logger.info(f"已添加 {len(internal_links)} 个内部链接到站点地图")
                
                # 5. 对新链接应用规则匹配
                if self.content_type_matcher:
                    for link in internal_links:
                        link_url = link['url']
                        matched_type = self.content_type_matcher.match_content_type(link_url)
                        
                        if matched_type and matched_type != 'unknown':
                            # 更新content_type_rule字段
                            SiteMap.objects.filter(
                                site_id=self.site.id,
                                url=link_url
                            ).update(content_type_rule=matched_type)
                            
                            logger.debug(f"URL {link_url} 规则匹配结果: {matched_type}")
            
            # 6. 调用AI分析当前URL，传递站点域名作为user_id
            user_id = self.site.domain  # 使用站点域名作为用户ID
            ai_result = ai_service.analyze_url_content_type(url, html_content=html_content, user_id=user_id)
            
            # 检查AI结果的有效性
            if not isinstance(ai_result, dict):
                logger.error(f"AI服务返回非字典类型: {type(ai_result)} - {ai_result}")
                ai_result = {'success': False, 'error': f'AI服务返回无效类型: {type(ai_result)}'}
            
            if ai_result.get('success'):
                logger.info(f"AI分析成功: {url} -> {ai_result.get('content_type')}")
                # 添加额外信息
                ai_result['url'] = url
                ai_result['new_links_count'] = new_links_count
                ai_result['html_content_length'] = len(html_content)
            else:
                logger.error(f"AI分析失败: {ai_result.get('error')}")
                ai_result['url'] = url
            
            return ai_result
            
        except Exception as e:
            logger.error(f"HTML预抓取分析失败 {url}: {e}")
            return {'success': False, 'error': str(e), 'url': url}
    
    def update_sitemap_content_type(self, sitemap: SiteMap, ai_result: Dict[str, Any]) -> bool:
        """
        根据AI分析结果更新SiteMap的content_type_ai字段
        
        Args:
            sitemap: SiteMap对象
            ai_result: AI分析结果
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if ai_result.get('success'):
                new_content_type = ai_result.get('content_type')
                sitemap.content_type_ai = new_content_type
                sitemap.save(update_fields=['content_type_ai', 'updated_at'])
                logger.info(f"URL类型已更新为: {new_content_type}")
                return True
            else:
                error_message = ai_result.get('error', '未知错误')
                logger.error(f"AI分析失败，无法更新: {error_message}")
                return False
        except Exception as e:
            logger.error(f"更新SiteMap失败: {e}")
            return False
    
    def analyze_sitemap_content(self, sitemap: SiteMap) -> Dict[str, Any]:
        """
        分析SiteMap的内容类型（推荐方法）
        
        Args:
            sitemap: SiteMap对象
            
        Returns:
            AI分析结果
        """
        try:
            logger.info(f"开始分析SiteMap内容: {sitemap.url}")
            
            # 通过SiteMap获取HTML内容（利用缓存机制）
            html_content = sitemap.get_html()
            if not html_content:
                return {'success': False, 'error': 'HTML内容不可用', 'url': sitemap.url}
            
            # 调用AI分析，传递站点域名作为user_id
            from apps.website.models import Site
            site = Site.objects.get(id=sitemap.site_id)
            user_id = site.domain
            ai_result = ai_service.analyze_url_content_type(
                sitemap.url, html_content=html_content, user_id=user_id
            )
            
            # 检查AI结果的有效性
            if not isinstance(ai_result, dict):
                logger.error(f"AI服务返回非字典类型: {type(ai_result)} - {ai_result}")
                ai_result = {'success': False, 'error': f'AI服务返回无效类型: {type(ai_result)}'}
            
            if ai_result.get('success'):
                logger.info(f"AI分析成功: {sitemap.url} -> {ai_result.get('content_type')}")
                ai_result['url'] = sitemap.url
                ai_result['html_content_length'] = len(html_content)
            else:
                logger.error(f"AI分析失败: {ai_result.get('error')}")
                ai_result['url'] = sitemap.url
            
            return ai_result
            
        except Exception as e:
            logger.error(f"分析SiteMap内容失败 {sitemap.url}: {e}")
            return {'success': False, 'error': str(e), 'url': sitemap.url}
    
    def analyze_and_update_sitemap(self, sitemap: SiteMap) -> Dict[str, Any]:
        """
        分析SiteMap并更新记录（推荐方法）
        
        Args:
            sitemap: SiteMap对象
            
        Returns:
            Dict: 处理结果
        """
        # 执行AI分析（使用新的方法）
        ai_result = self.analyze_sitemap_content(sitemap)
        
        # 更新数据库
        update_success = self.update_sitemap_content_type(sitemap, ai_result)
        
        # 返回综合结果
        result = ai_result.copy()
        result['update_success'] = update_success
        result['sitemap_id'] = sitemap.id
        
        return result 