#!/usr/bin/env python
"""
测试重构后的AIContentAnalyzer功能
验证通过SiteMap获取HTML的架构是否正常工作
"""

from base import init_debug
init_debug()

from apps.website.models import Site, SiteMap
from apps.website.service.ai_content_analyzer import AIContentAnalyzer

def test_refactored_analyzer():
    """测试重构后的分析器"""
    
    print("测试重构后的AIContentAnalyzer")
    print("=" * 50)
    
    # 选择一个测试站点
    try:
        site = Site.objects.first()
        if not site:
            print("X 没有找到测试站点")
            return
        
        print(f"使用测试站点: {site.name} ({site.domain})")
        
        # 选择一个SiteMap记录
        sitemap = SiteMap.objects.filter(site_id=site.id).first()
        if not sitemap:
            print("X 没有找到SiteMap记录")
            return
        
        print(f"测试URL: {sitemap.url}")
        
        # 创建分析器
        analyzer = AIContentAnalyzer(site)
        
        # 测试新的analyze_sitemap_content方法
        print("\n1. 测试 analyze_sitemap_content 方法:")
        result = analyzer.analyze_sitemap_content(sitemap)
        
        print(f"   结果: {'成功' if result.get('success') else '失败'}")
        if result.get('success'):
            print(f"   内容类型: {result.get('content_type')}")
            print(f"   HTML长度: {result.get('html_content_length')}")
        else:
            print(f"   错误信息: {result.get('error')}")
        
        # 测试analyze_and_update_sitemap方法
        print("\n2. 测试 analyze_and_update_sitemap 方法:")
        result = analyzer.analyze_and_update_sitemap(sitemap)
        
        print(f"   分析结果: {'成功' if result.get('success') else '失败'}")
        print(f"   更新结果: {'成功' if result.get('update_success') else '失败'}")
        print(f"   SiteMap ID: {result.get('sitemap_id')}")
        
        # 验证HTML获取方式
        print("\n3. 验证HTML获取方式:")
        html_content = sitemap.get_html()
        if html_content:
            print(f"   成功通过SiteMap.get_html()获取HTML，长度: {len(html_content)}")
        else:
            print("   通过SiteMap.get_html()获取HTML失败")
        
        print("\n" + "=" * 50)
        print("重构测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_refactored_analyzer()