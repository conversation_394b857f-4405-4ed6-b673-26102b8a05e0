from typing import Dict, Any, List, Optional
import logging
import time
import random
import re
import os
import asyncio
from typing import Dict, List, Any
from django.utils import timezone 
from playwright.async_api import async_playwright, Page
from apps.douyin.common.utils import extract_video_id
from apps.douyin.models import <PERSON>uy<PERSON>Account, DouyinAccountVideo, DouyinAccountVideoSnapshot
from apps.douyin.enums import DouyinContentType
from contextlib import asynccontextmanager

from apps.douyin.common.crawler_config import DOUYIN_CONFIG, SELECTORS

logger = logging.getLogger(__name__)

# 视频删除检测关键字
VIDEO_DELETED_KEYWORD = "item_non_existent_recommend_auto"

def get_random_user_data_dir() -> str:
    """
    随机获取一个用户数据目录（从1号开始，不选择0号文件夹）
    
    Returns:
        str: 用户数据目录路径
    """
    user_data_dirs = DOUYIN_CONFIG['user_data_dirs']
    if not user_data_dirs:
        raise ValueError("用户数据目录列表为空")
    
    # 检查是否有足够的文件夹（至少需要2个，才能跳过0号选择1号及以后）
    if len(user_data_dirs) <= 1:
        raise ValueError("用户数据目录列表至少需要2个目录才能跳过0号文件夹")
    
    # 从1号文件夹开始选择，不选择0号文件夹
    available_dirs = user_data_dirs[1:]  # 跳过索引0，从索引1开始
    random_number = random.randint(6, 1000)
    remainder = random_number % len(available_dirs)
    return available_dirs[remainder]

class DouyinCrawler:
    """抖音网页爬虫类"""
    
    def __init__(self, headless=None, timeout=None, user_agent=None):
        """初始化爬虫"""
        self.headless = headless if headless is not None else DOUYIN_CONFIG['headless']
        self.timeout = timeout if timeout is not None else DOUYIN_CONFIG['timeout']
        self.user_agent = user_agent if user_agent is not None else DOUYIN_CONFIG['user_agent']
        
    @asynccontextmanager
    async def _setup_browser(self, user_data_dir: Optional[str] = None, headless: Optional[bool] = None):
        """设置浏览器环境的异步上下文管理器
        
        Args:
            user_data_dir: 用户数据目录路径，如果为 None 则随机选择
            headless: 是否使用无头模式，如果为 None 则使用配置文件中的设置
        """
        if user_data_dir is None:
            user_data_dir = get_random_user_data_dir()
        
        # 如果没有明确指定headless，则使用配置文件中的值
        if headless is None:
            headless = self.headless
        
        playwright = None
        browser_context = None
        page = None

        try:
            # 确保用户数据目录存在
            os.makedirs(user_data_dir, exist_ok=True)
            logger.info(f"使用用户数据目录: {user_data_dir}")
            logger.info(f"浏览器模式: {'无头模式' if headless else '有头模式'}")

            # 启动 Playwright
            playwright = await async_playwright().start()

            # 启动浏览器上下文
            browser_context = await playwright.chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                headless=headless,
                user_agent=self.user_agent,
                viewport={'width': 1920, 'height': 1080},
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

            # 创建新页面
            page = await browser_context.new_page()
            page.set_default_timeout(self.timeout)

            yield page

        except Exception as e:
            logger.error(f"设置浏览器环境失败: {str(e)}")
            raise
        finally:
            # 清理资源
            try:
                if page:
                    await page.close()
                if browser_context:
                    await browser_context.close()
                if playwright:
                    await playwright.stop()
            except Exception as e:
                logger.warning(f"清理浏览器资源时出现警告: {str(e)}")
    

    
    @staticmethod
    def validate_unique_id(account: DouyinAccount):
        """
        验证账号unique_id格式
        
        Args:
            account: DouyinAccount模型对象
            
        Returns:
            bool: 验证结果，True表示格式符合规范，False表示不符合
        """
        if not account or not account.unique_id:
            return False
        pattern = r'^MS4wLjABAAAA[A-Za-z0-9\-_]{30,100}$'
        return bool(re.match(pattern, account.unique_id))
            
    
    async def fetch_account_info(self, account: DouyinAccount) -> DouyinAccount:
        """
        抓取账号信息
        
        Args:
            account: DouyinAccount模型对象
            
        Returns:
            DouyinAccount: 更新后的账号对象
        """
        if not account:
            raise ValueError("账号对象不能为空")
        
        if not account.unique_id:
            raise ValueError("账号唯一ID不能为空")
        
        if not DouyinCrawler.validate_unique_id(account):
            raise ValueError(f"账号unique_id格式不符合规范: {account.unique_id}")
        
        account_url = f"https://www.douyin.com/user/{account.unique_id}"
        logger.info(f"开始抓取账号信息: URL: {account_url}")
        
        # 为特定账号使用固定的用户数据目录，避免登录状态丢失
        user_data_dirs = DOUYIN_CONFIG['user_data_dirs']
        user_data_dir = user_data_dirs[0] if user_data_dirs and len(user_data_dirs) > 0 else None
        if not user_data_dir:
            user_data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'crawl_data/browser_data/douyin_user_0')
            os.makedirs(user_data_dir, exist_ok=True)
        
        logger.info(f"将使用用户数据目录: {user_data_dir}")
        
        retries = DOUYIN_CONFIG['retry_count']
        for attempt in range(retries):
            try:
                async with self._setup_browser(user_data_dir) as page:
                    # 设置超时时间
                    original_timeout = DOUYIN_CONFIG['timeout']
                    # 加长超时时间到120秒
                    extended_timeout = 40000  # 设置为40秒
                    logger.info(f"设置页面默认超时时间: {extended_timeout}ms (原超时: {original_timeout}ms)")
                    page.set_default_timeout(extended_timeout)
                    
                    # 打开账号主页前记录
                    logger.info(f"开始导航到抖音账号主页: {account_url}")
                    
                    # 导航到账号主页
                    response = await page.goto(account_url)
                    if response:
                        logger.info(f"页面导航响应状态: {response.status} - {response.status_text}")
                    else:
                        logger.warning("未获取到响应对象")
                    
                    # 检查是否启用版本检测
                    from apps.douyin.common.crawler_config import PAGE_VERSION_CONFIG
                    use_version_detection = PAGE_VERSION_CONFIG.get('enabled', False)
                    
                    # 进行版本检测并获取选择器配置
                    if use_version_detection:
                        logger.info("开始检测页面版本...")
                        from apps.douyin.common.version_detector import version_detector
                        try:
                            detected_version, version_selectors = await version_detector.detect_and_get_selectors(page, 'user_page')
                            logger.info(f"检测到页面版本: {detected_version}")
                            account_selectors = version_selectors['account']
                        except Exception as e:
                            logger.error(f"页面版本检测失败: {str(e)}")
                            raise RuntimeError(f"无法检测页面版本，爬取失败: {str(e)}")
                    else:
                        # 如果版本检测被禁用，使用默认选择器
                        logger.info("版本检测已禁用，使用默认选择器")
                        account_selectors = SELECTORS['account']
                    
                    # 等待关键元素可见
                    nickname_selector = account_selectors['nickname']
                    logger.info(f"等待昵称元素可见: {nickname_selector}")
                    await page.wait_for_selector(nickname_selector, state="visible", timeout=30 * 1000)
                    logger.info("昵称元素已可见")
                    
                    logger.info("开始从页面提取账号信息")
                    
                    # 从页面提取账号信息，直接传递选择器配置
                    from apps.douyin.service.parsers import DouyinParser
                    parser = DouyinParser()
                    
                    logger.info("调用解析器提取账号信息")
                    account_info = await parser.parse_account_info(page, account_selectors=account_selectors)
                    
                    # 验证解析结果
                    if not account_info:
                        logger.error("账号信息解析结果为空")
                    else:
                        logger.info(f"成功获取账号信息: {account_info.keys()}")
                        for key, value in account_info.items():
                            logger.info(f"  - {key}: {value}")
                    
                    # 添加 unique_id 字段到账号信息中
                    if account_info:
                        account_info['unique_id'] = account.unique_id
                        logger.info(f"已将 unique_id: {account.unique_id} 添加到账号信息中")
                    
                    logger.info("账号信息抓取成功")
                    
                    # 更新DouyinAccount对象
                    for key, value in account_info.items():
                        if hasattr(account, key) and value is not None:
                            setattr(account, key, value)
                    await asyncio.to_thread(account.save)
                    
                    return account
                    
            except Exception as e:
                error_msg = str(e)
                logger.error(f"抓取账号信息失败 (尝试 {attempt+1}/{retries}): {error_msg}", exc_info=True)
                
                # 详细记录错误类型以便调试
                if "Timeout" in error_msg:
                    logger.error(f"发生超时错误，超时限制: {DOUYIN_CONFIG['timeout']}ms")
                elif "Navigation failed" in error_msg:
                    logger.error("导航失败，可能是网络问题或目标网站不可达")
                elif "Target closed" in error_msg:
                    logger.error("目标关闭，浏览器可能被意外关闭")
                elif "Protocol error" in error_msg:
                    logger.error("协议错误，浏览器通信异常")
                
                if attempt == retries - 1:
                    logger.error(f"已达到最大重试次数({retries})，放弃抓取")
                    raise
                
                logger.info(f"将进行第 {attempt+2} 次重试")
        
        # 这里实际上不会执行到，因为上面的循环在最后一次尝试失败后会抛出异常
        raise RuntimeError("抓取账号信息失败，已超过最大重试次数")
    
    async def fetch_video_list(self, account: DouyinAccount, target_count: int = DOUYIN_CONFIG['update_video_count'], _stop_video_id: Optional[str] = None, user_data_dir: Optional[str] = None, headless: Optional[bool] = None) -> List[DouyinAccountVideo]:
        """
        抓取视频列表
        
        Args:
            account: DouyinAccount模型对象
            target_count: 目标视频数量
            _stop_video_id: 停止视频ID，当抓取到此视频ID时停止抓取（内部参数）
            user_data_dir: 用户数据目录路径，如果为 None 则随机选择
            headless: 是否使用无头模式，如果为 None 则使用配置文件中的设置
            
        Returns:
            List[DouyinAccountVideo]: 视频对象列表
        """
        if not account.unique_id:
            raise ValueError("账号唯一ID不能为空")
        
        if not DouyinCrawler.validate_unique_id(account):
            raise ValueError(f"账号unique_id格式不符合规范: {account.unique_id}")
        
        account_url = f"https://www.douyin.com/user/{account.unique_id}"
        logger.info(f"开始抓取账号视频列表: URL: {account_url}, 目标数量: {target_count}")
        
        retries = DOUYIN_CONFIG['retry_count']
        for attempt in range(retries):
            try:
                async with self._setup_browser(user_data_dir, headless) as page:
                    # 打开账号主页 - 使用更长的超时时间
                    logger.info(f"开始导航到账号主页: {account_url}")
                    await page.goto(account_url, timeout=DOUYIN_CONFIG['timeout'], wait_until="domcontentloaded")
                    
                    # 集成版本检测功能
                    from apps.douyin.common.version_detector import version_detector
                    logger.info("开始检测页面版本...")
                    detected_version, version_selectors = await version_detector.detect_and_get_selectors(page, 'user_page')
                    logger.info(f"检测到页面版本: {detected_version}")
                    
                    # 使用检测到的版本对应的选择器
                    video_list_css = version_selectors['video_list']['container']
                    logger.info(f"使用版本 {detected_version} 的视频列表选择器: {video_list_css}")
                    
                    # 等待目标视频列表元素出现，使用配置文件中的超时时间
                    logger.info(f"等待视频列表元素出现: {video_list_css}")
                    await page.wait_for_selector(video_list_css, timeout=DOUYIN_CONFIG['timeout'])
                    
                    # 执行无限滚动获取视频列表，传递版本选择器
                    video_list_dict = await self._scroll_and_extract_videos(page, target_count, version_selectors, _stop_video_id)
                    
                    # 为每个视频添加 account_unique_id 字段，关联到账号
                    for video in video_list_dict:

                        
                        video['account_unique_id'] = account.unique_id
                    
                    logger.info(f"账号视频列表抓取成功，共 {len(video_list_dict)} 个视频")
                    
                    # 将字典列表转换为 DouyinAccountVideo 对象列表
                    from django.utils import timezone
                    current_time = timezone.now()
                    
                    video_objects = []
                    for video_info in video_list_dict:
                        # 确保所有必填字段都有值
                        if not video_info.get('id') or not video_info.get('video_id'):
                            continue
                        
                        video_object = DouyinAccountVideo(
                            id=video_info.get('id') or video_info.get('video_id'),  # 确保ID字段有值
                            account_unique_id=account.unique_id,
                            title=video_info.get('title', ''),
                            url=video_info.get('url', ''),
                            like_count=video_info.get('like_count'),
                            comment_count=video_info.get('comment_count'),
                            collect_count=video_info.get('collect_count'),
                            share_count=video_info.get('share_count'),
                            content_type=video_info.get('content_type', DouyinContentType.VIDEO.value),
                            issue_time=video_info.get('issue_time'),
                            create_time=current_time,
                            update_time=current_time
                        )
                        video_objects.append(video_object)
                    
                    return video_objects
                    
            except Exception as e:
                logger.error(f"抓取账号视频列表失败 (尝试 {attempt+1}/{retries}): {str(e)}", exc_info=True)
                
                if attempt == retries - 1:
                    raise
                
                logger.info(f"将进行第 {attempt+2} 次重试")
        
        # 这里实际上不会执行到，因为上面的循环在最后一次尝试失败后会抛出异常
        raise RuntimeError("抓取账号视频列表失败，已超过最大重试次数")
    
    async def fetch_video_list_stop_id(self, account: DouyinAccount, stop_video_id: str, user_data_dir: Optional[str] = None, headless: Optional[bool] = None) -> List[DouyinAccountVideo]:
        """
        抓取视频列表直到遇到指定的视频ID
        
        Args:
            account: DouyinAccount模型对象
            stop_video_id: 截止视频ID，当抓取到此视频ID时停止抓取
            user_data_dir: 用户数据目录路径，如果为 None 则使用 douyin_user_0（已登录用户文件夹）
            headless: 是否使用无头模式，如果为 None 则默认使用有头模式（保持登录状态稳定）
            
        Returns:
            List[DouyinAccountVideo]: 视频对象列表
        """
        if not stop_video_id:
            raise ValueError("截止视频ID不能为空")
        
        # 如果没有指定用户数据目录，使用 douyin_user_0（已登录的用户文件夹）
        if user_data_dir is None:
            user_data_dir = DOUYIN_CONFIG['user_data_dirs'][0]
        
        # 如果没有明确指定headless，默认使用有头模式以保持登录状态稳定
        if headless is None:
            headless = False
        
        logger.info(f"开始抓取账号视频列表直到视频ID: {stop_video_id}，使用用户数据目录: {user_data_dir}，浏览器模式: {'无头模式' if headless else '有头模式'}")
        
        # 调用 fetch_video_list 方法，传递内部参数
        # 设置一个较大的 target_count 作为最大限制，避免无限滚动
        max_target_count = 2000
        return await self.fetch_video_list(account, target_count=max_target_count, _stop_video_id=stop_video_id, user_data_dir=user_data_dir, headless=headless)
    
    async def _scroll_and_extract_videos(self, page: Page, target_count: int, version_selectors: Optional[dict] = None, stop_video_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        滚动页面并提取视频信息
        
        Args:
            page: Playwright页面对象
            target_count: 目标视频数量
            version_selectors: 版本对应的选择器配置，如果为None则使用默认选择器
            stop_video_id: 截止视频ID，当遇到此视频ID时停止滚动，默认为None
            
        Returns:
            List[Dict[str, Any]]: 视频信息列表
        """
        from apps.douyin.service.parsers import DouyinParser
        parser = DouyinParser()
        
        video_list = []
        # 使用传入的版本选择器，如果没有则使用默认选择器
        if version_selectors:
            selector = version_selectors['video_list']['container']
        else:
            selector = SELECTORS['video_list']['container']
        
        # 初始等待，确保页面完全加载
        logger.info("初始等待，确保页面完全加载...")
        await page.wait_for_timeout(2000)  # 增加初始等待时间
        
        # 获取页面尺寸
        page_size = await page.evaluate("""() => {
            return {
                width: window.innerWidth,
                height: window.innerHeight
            }
        }""")
        
        # 计算视频区域的位置（避开左侧导航栏和顶部信息区）
        target_x = int(page_size['width'] * 0.6)  # 页面宽度的60%位置
        target_y = int(page_size['height'] * 0.5)  # 页面高度的50%位置
        
        logger.info(f"将鼠标移动到页面中央区域: x={target_x}, y={target_y}")
        
        # 移动鼠标到目标位置
        await page.mouse.move(target_x, target_y)
        await page.wait_for_timeout(500)  # 短暂等待
        
        # 设置最大滚动次数
        max_scroll_attempts = max(100, target_count // 3)
        scroll_attempts = 0
        
        # 记录已处理的视频ID，避免重复
        processed_video_ids = set()
        
        # 记录连续没有新视频的次数
        consecutive_no_new_videos = 0
        # 最大连续没有新视频的次数
        max_consecutive_no_new_videos = 3
        
        while scroll_attempts < max_scroll_attempts:
            scroll_attempts += 1
            logger.info(f"执行第 {scroll_attempts} 次滚动")
            
            # 直接使用鼠标滚轮模拟，使用配置文件中的滚动距离
            wheel_delta = DOUYIN_CONFIG['scroll_distance']
            logger.info(f"执行鼠标滚轮操作，滚动距离: {wheel_delta}")
            await page.mouse.wheel(0, wheel_delta)
            
            # 等待内容加载，使用配置文件中的滚动时间间隔
            wait_time = DOUYIN_CONFIG['scroll_interval']
            logger.info(f"等待内容加载: {wait_time}ms")
            await page.wait_for_timeout(wait_time)
            
            # 每隔几次滚动，随机移动一下鼠标位置（模拟真实用户行为）
            if scroll_attempts % 3 == 0:
                # 在视频区域内随机移动，但确保不会移动到导航栏或顶部区域
                # 水平方向：保持在页面中央区域，避开左侧导航栏
                safe_x_min = int(page_size['width'] * 0.3)  # 避开左侧30%区域
                safe_x_max = int(page_size['width'] * 0.9)  # 避开右侧10%区域
                
                # 垂直方向：避开顶部信息区
                safe_y_min = int(page_size['height'] * 0.2)  # 避开顶部20%区域
                safe_y_max = int(page_size['height'] * 0.8)  # 避开底部20%区域
                
                random_x = random.randint(safe_x_min, safe_x_max)
                random_y = random.randint(safe_y_min, safe_y_max)
                
                logger.info(f"随机移动鼠标到安全区域: x={random_x}, y={random_y}")
                await page.mouse.move(random_x, random_y)
                await page.wait_for_timeout(500)
            
            # 获取视频列表
            elements = await page.query_selector_all(selector)
            current_count = len(elements)
            logger.info(f"当前发现视频元素数量: {current_count}个")
            
            # 提取视频信息
            new_video_count = 0
            should_stop = False
            for element in elements:
                try:
                    # 传递版本选择器给解析器
                    video_info = await parser.parse_video_info_from_element(page, element, version_selectors)
                    if video_info and video_info.get('video_id') and video_info['video_id'] not in processed_video_ids:
                        video_list.append(video_info)
                        processed_video_ids.add(video_info['video_id'])
                        new_video_count += 1
                        
                        # 检查是否遇到截止视频ID
                        if stop_video_id and video_info['video_id'] == stop_video_id:
                            logger.info(f"遇到截止视频ID: {stop_video_id}，停止抓取")
                            should_stop = True
                            break
                except Exception as e:
                    logger.warning(f"提取视频信息失败: {str(e)}")
            
            # 如果遇到截止视频ID，停止滚动
            if should_stop:
                logger.info(f"因遇到截止视频ID停止滚动，当前共有 {len(video_list)} 个视频")
                break
            
            logger.info(f"本次新增 {new_video_count} 个视频，当前共有 {len(video_list)} 个视频")
            
            # 如果已经达到目标视频数量，则停止滚动
            if len(video_list) >= target_count:
                logger.info(f"已达到目标视频数量: {target_count}")
                break
            
            # 如果没有新视频，增加连续没有新视频的计数
            if new_video_count == 0:
                consecutive_no_new_videos += 1
                logger.info(f"连续 {consecutive_no_new_videos} 次没有新视频")
                
                # 如果连续多次没有新视频，停止滚动
                if consecutive_no_new_videos >= max_consecutive_no_new_videos:
                    logger.info(f"连续 {max_consecutive_no_new_videos} 次没有新视频，停止滚动")
                    break
            else:
                # 重置连续没有新视频的计数
                consecutive_no_new_videos = 0
                
            # 如果连续多次没有新视频，尝试其他操作
                if scroll_attempts % 5 == 0:
                    # 确保鼠标位于视频区域中央
                    logger.info("重新定位鼠标到视频区域中央")
                    center_x = int(page_size['width'] * 0.6)
                    center_y = int(page_size['height'] * 0.5)
                    await page.mouse.move(center_x, center_y)
                    await page.wait_for_timeout(800)
                    
                    # 尝试更大范围的滚动
                    logger.info("尝试更大范围的滚动...")
                    await page.mouse.wheel(0, 1000)
                    await page.wait_for_timeout(2500)
    
        logger.info(f"滚动结束，共发现 {len(video_list)} 个视频")
        return video_list


    async def fetch_video_detail(self, video: DouyinAccountVideo) -> tuple[DouyinAccountVideo, DouyinAccountVideoSnapshot]:
        """
        抓取视频详情
        
        Args:
            video: DouyinAccountVideo 模型对象
            
        Returns:
            tuple: (更新后的视频对象, 视频快照对象)
        """
        url = video.url
        logger.info(f"开始抓取视频详情: {url}")
        
        # 初始化快照对象
        video_snapshot = DouyinAccountVideoSnapshot(
            video_id=video.id,
            like_count=None,
            comment_count=None,
            collect_count=None,
            share_count=None,
            snapshot_time=timezone.now()
        )
        
        retries = DOUYIN_CONFIG['retry_count']
        for attempt in range(retries):
            try:
                async with self._setup_browser() as page:
                    # 打开视频详情页
                    await page.goto(url)
                    
                    # 检查是否启用版本检测
                    from apps.douyin.common.crawler_config import PAGE_VERSION_CONFIG
                    use_version_detection = PAGE_VERSION_CONFIG.get('enabled', False)
                    
                    # 根据内容类型决定页面类型参数
                    if video.content_type == DouyinContentType.NOTE.value:
                        page_type = 'note_detail_page'
                        logger.info("内容类型为图文，使用图文详情页配置")
                    else:
                        page_type = 'video_detail_page'
                        logger.info("内容类型为视频，使用视频详情页配置")
                    
                    # 进行版本检测并获取选择器配置
                    if use_version_detection:
                        logger.info(f"开始检测{page_type}版本...")
                        from apps.douyin.common.version_detector import version_detector
                        try:
                            detected_version, version_selectors = await version_detector.detect_and_get_selectors(page, page_type)
                            logger.info(f"检测到{page_type}版本: {detected_version}")
                        except Exception as e:
                            logger.error(f"{page_type}版本检测失败: {str(e)}")
                            raise RuntimeError(f"无法检测{page_type}版本，爬取失败: {str(e)}")
                    else:
                        # 如果版本检测被禁用，使用默认选择器
                        logger.info("版本检测已禁用，使用默认选择器")
                        version_selectors = SELECTORS
                    
                    from apps.douyin.service.parsers import DouyinParser
                    parser = DouyinParser()
                    
                    # 根据内容类型选择合适的解析方法
                    try:
                        if video.content_type == DouyinContentType.NOTE.value:
                            # 笔记类型
                            note_selectors = version_selectors['note_detail']
                            
                            # 等待关键元素可见
                            await page.wait_for_selector(note_selectors['like_count'], timeout=40 * 1000)
                            
                            logger.info("解析笔记详情")
                            parsed_info = await parser.parse_note_detail(page, note_selectors=note_selectors)
                        else:
                            # 视频类型
                            video_detail_selectors = version_selectors['video_detail']
                            
                            # 等待关键元素可见
                            await page.wait_for_selector(video_detail_selectors['issue_time'], timeout=40 * 1000)
                            
                            logger.info("解析视频详情")
                            parsed_info = await parser.parse_video_detail(page, video_detail_selectors=video_detail_selectors)
                            
                    except Exception as selector_error:
                        # 等待选择器失败时，检查是否因为页面跳转（视频删除）
                        logger.warning(f"等待选择器失败，检查是否为页面跳转: {str(selector_error)}")
                        
                        # 检查URL是否包含视频删除关键字（视频删除检测）
                        current_url = page.url
                        logger.info(f"原始URL: {url}")
                        logger.info(f"当前URL: {current_url}")
                        
                        # 检查当前URL是否包含视频删除关键字
                        # 如果包含该关键字，说明视频已被删除
                        if VIDEO_DELETED_KEYWORD in current_url:
                            logger.info(f"检测到视频删除：当前URL包含{VIDEO_DELETED_KEYWORD}")
                            logger.info(f"原始URL: {url}")
                            logger.info(f"当前URL: {current_url}")
                            
                            # 创建自定义异常来标识视频删除
                            class VideoDeletedException(Exception):
                                def __init__(self, video_id, original_url, redirect_url):
                                    self.video_id = video_id
                                    self.original_url = original_url
                                    self.redirect_url = redirect_url
                                    super().__init__(f"视频 {video_id} 已被删除")
                            
                            raise VideoDeletedException(
                                video_id=str(video.id),
                                original_url=url,
                                redirect_url=current_url
                            )
                        
                        # 如果当前URL不包含视频删除关键字，可能是网络问题或页面加载慢
                        logger.info(f"当前URL不包含视频删除关键字({VIDEO_DELETED_KEYWORD})，可能是网络问题或页面加载慢")
                        raise selector_error
                    
                    # 检查是否至少有一个关键字段被成功解析
                    has_data = any([
                        parsed_info.get('like_count') is not None,
                        parsed_info.get('comment_count') is not None,
                        parsed_info.get('collect_count') is not None,
                        parsed_info.get('share_count') is not None,
                        parsed_info.get('issue_time') is not None
                    ])
                    
                    if has_data:
                        logger.info(f"视频详情抓取成功")
                        
                        # 更新视频对象
                        if parsed_info.get('like_count') is not None:
                            video.like_count = parsed_info['like_count']
                            video_snapshot.like_count = parsed_info['like_count']
                            
                        if parsed_info.get('comment_count') is not None:
                            video.comment_count = parsed_info['comment_count']
                            video_snapshot.comment_count = parsed_info['comment_count']
                            
                        if parsed_info.get('collect_count') is not None:
                            video.collect_count = parsed_info['collect_count']
                            video_snapshot.collect_count = parsed_info['collect_count']
                            
                        if parsed_info.get('share_count') is not None:
                            video.share_count = parsed_info['share_count']
                            video_snapshot.share_count = parsed_info['share_count']
                            
                        if parsed_info.get('issue_time') is not None:
                            video.issue_time = parsed_info['issue_time']
                        
                        video.update_time = timezone.now()
                        
                        return video, video_snapshot
                    else:
                        # 数据解析失败，抛出异常
                        raise ValueError("未能提取视频详情信息")
                        
            except Exception as e:
                # 检查是否为视频删除异常
                if e.__class__.__name__ == "VideoDeletedException":
                    logger.info(f"检测到视频已被删除: {str(e)}")
                    # 视频删除不需要重试，直接抛出异常
                    raise e
                
                logger.error(f"抓取视频详情失败 (尝试 {attempt+1}/{retries}): {str(e)}", exc_info=True)
                
                if attempt == retries - 1:
                    raise RuntimeError(f"抓取视频详情失败，已超过最大重试次数: {str(e)}") 
                
                logger.info(f"将进行第 {attempt+2} 次重试")

        # 这里实际上不会执行到，因为上面的循环在最后一次尝试失败后会抛出异常
        raise RuntimeError("抓取视频详情失败，已超过最大重试次数")