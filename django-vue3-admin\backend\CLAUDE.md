# Django-Vue3-Admin 汇远轻媒2

## 项目概述
大型文本内容纠错和新媒体数据统计分析系统，主要服务于政府机构和媒体单位的文本内容错敏词校对和新媒体数据统计分析报告。

## 技术栈
- **核心**: Django 4.2.14 + DVAdmin + Vue 3
- **数据库**: MySQL + PostgreSQL + Redis
- **爬虫**: Playwright + Selenium + Scrapy
- **AI集成**: Dify + OpenAI + 扣子(Coze) + SiliconFlow
- **消息队列**: Celery + RabbitMQ

## 核心模块
- **website**: 通用网站爬虫 (AI+Playwright)
- **wechat_official_account**: 微信公众号爬虫
- **douyin/weibo**: 社交媒体爬虫
- **regulate**: 内容监管校对系统
- **regulate_jiaodui**: AI智能校对服务
- **operation_report**: 运营报告生成
- **ranking**: 媒体影响力排行 (WCI/BCI/DCI)
- **maintain**: 系统维护监控
- **mcp**: 

## 特殊配置

### 多环境支持
- `conf/env_*.py`: dev/test/prod/tencent_prod/tencent_test
- 多数据库路由: MySQL(业务) + PostgreSQL(分析) + Redis(缓存)

### 部署配置  
- Docker + Gunicorn + Supervisor + Nginx

## 开发规范

### 架构模式
- **模块化**: apps模块独立，service层处理业务逻辑
- **调试脚本**: debugs目录统一管理调试脚本

### 调试脚本规范

**标准导入**:
```python
from base import init_debug
init_debug()
```

**base.py内容**:
```python
def init_debug():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    while os.path.basename(current_dir) != "backend":
        current_dir = os.path.dirname(current_dir)
    sys.path.append(current_dir)
    from debug_init import django_setup
    django_setup()
```

## 关键注意事项

### 安全
- API密钥通过环境变量管理
- 爬虫遵守robots.txt和频率限制

### 性能  
- Redis缓存热点数据
- Celery异步处理耗时任务
- 避免N+1查询

