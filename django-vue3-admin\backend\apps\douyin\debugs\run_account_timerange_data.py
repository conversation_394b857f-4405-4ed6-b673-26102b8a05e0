# 设置环境变量为dev环境
import os
os.environ['DJANGO_ENV'] = 'prod'

from base import init_debug
init_debug()

import asyncio
import logging
import nest_asyncio
from datetime import datetime
from typing import List, Dict, Any

from apps.douyin.service.douyin_time_range_service import DouyinTimeRangeService

# 应用 nest_asyncio 以允许在已有事件循环中运行新的事件循环
nest_asyncio.apply()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ================================================================================
# Excel数据读取配置
# ================================================================================

# Excel文件配置
EXCEL_FILE_NAME = "内蒙古党史研究室-抖音数据抓取.xlsx"  # 可以修改为你的Excel文件名

# 是否使用Excel数据（True=从Excel读取，False=使用下面的手动配置）
USE_EXCEL_DATA = True

# ================================================================================
# 时间戳转换辅助函数
# ================================================================================

def datetime_str_to_timestamp(datetime_str: str) -> int:
    """
    将日期时间字符串转换为时间戳
    
    Args:
        datetime_str: 日期时间字符串，支持格式：
            - "2025-06-20" (只有日期，时间默认为00:00:00)
            - "2025-06-20 13:19:26" (完整日期时间)
            
    Returns:
        int: 时间戳
        
    示例:
        datetime_str_to_timestamp("2025-06-20")  # 2025-06-20 00:00:00
        datetime_str_to_timestamp("2025-06-20 13:19:26")  # 2025-06-20 13:19:26
    """
    try:
        # 尝试解析完整的日期时间格式
        if ' ' in datetime_str:
            dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
        else:
            # 只有日期，时间默认为00:00:00
            dt = datetime.strptime(datetime_str, "%Y-%m-%d")
        
        return int(dt.timestamp())
    except ValueError as e:
        raise ValueError(f"时间格式错误: {datetime_str}，支持格式: 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS'") from e

def datetime_to_timestamp(year: int, month: int, day: int, hour: int = 0, minute: int = 0, second: int = 0) -> int:
    """
    将日期时间转换为时间戳（保留原有函数以兼容）
    
    Args:
        year: 年
        month: 月
        day: 日
        hour: 时（可选，默认0）
        minute: 分（可选，默认0）
        second: 秒（可选，默认0）
        
    Returns:
        int: 时间戳
        
    示例:
        datetime_to_timestamp(2023, 12, 28)  # 2023-12-28 00:00:00
        datetime_to_timestamp(2024, 1, 4, 23, 59, 59)  # 2024-01-04 23:59:59
    """
    dt = datetime(year, month, day, hour, minute, second)
    return int(dt.timestamp())

# ================================================================================
# 手动配置区域 - 当USE_EXCEL_DATA=False时使用
# ================================================================================

# 手动测试账号配置列表（当不使用Excel时的备用配置）
MANUAL_TEST_ACCOUNTS_CONFIG = [
    {
        'unique_id': '',
        'start_time': datetime_str_to_timestamp("2019-06-01"),  # 使用日期字符串
        'end_time': datetime_str_to_timestamp("2020-05-31"),    # 使用日期字符串
        'stop_video_id': '6709763119629012231'
    },
]

# ================================================================================
# 数据获取函数
# ================================================================================

def get_accounts_config() -> List[Dict[str, Any]]:
    """
    获取账号配置列表
    
    Returns:
        List[Dict]: 账号配置列表
    """
    if USE_EXCEL_DATA:
        logger.info(f"从Excel文件读取账号配置: {EXCEL_FILE_NAME}")
        try:
            # 从Excel读取数据
            config_list = DouyinTimeRangeService.read_excel_and_convert(EXCEL_FILE_NAME)
            
            if not config_list:
                logger.warning("Excel文件中没有读取到有效数据，使用手动配置")
                return MANUAL_TEST_ACCOUNTS_CONFIG
            
            logger.info(f"从Excel成功读取 {len(config_list)} 个账号配置")
            
            # 打印读取到的配置
            logger.info("Excel读取的配置预览:")
            for i, config in enumerate(config_list, 1):
                start_dt = datetime.fromtimestamp(config['start_time'])
                end_dt = datetime.fromtimestamp(config['end_time'])
                unique_id_short = config['unique_id'][:20] + "..." if len(config['unique_id']) > 20 else config['unique_id']
                logger.info(f"  账号 {i}: {unique_id_short}, {start_dt.strftime('%Y-%m-%d')} 到 {end_dt.strftime('%Y-%m-%d')}, 停止ID: {config['stop_video_id']}")
            
            return config_list
            
        except Exception as e:
            logger.error(f"从Excel读取数据失败: {str(e)}")
            logger.info("使用手动配置作为备用")
            return MANUAL_TEST_ACCOUNTS_CONFIG
    else:
        logger.info("使用手动配置的账号列表")
        return MANUAL_TEST_ACCOUNTS_CONFIG

# ================================================================================
# 测试执行代码
# ================================================================================

class DouyinTimeRangeBatchTester:
    """抖音时间范围批量抓取测试器（包含账号信息抓取）"""
    
    def __init__(self):
        self.service = DouyinTimeRangeService()
    
    def validate_config(self, config: List[Dict[str, Any]]) -> bool:
        """验证配置参数"""
        if not config:
            logger.error("测试配置为空，请检查Excel文件或手动配置")
            return False
        
        required_fields = ['unique_id', 'start_time', 'end_time', 'stop_video_id']
        
        for i, account_config in enumerate(config, 1):
            for field in required_fields:
                if field not in account_config or not account_config[field]:
                    logger.error(f"账号 {i} 缺少必需字段: {field}")
                    return False
            
            # 验证时间戳
            try:
                start_dt = datetime.fromtimestamp(account_config['start_time'])
                end_dt = datetime.fromtimestamp(account_config['end_time'])
                
                if start_dt >= end_dt:
                    logger.error(f"账号 {i} 的开始时间必须早于结束时间")
                    return False
                    
            except (ValueError, OSError) as e:
                logger.error(f"账号 {i} 的时间戳格式错误: {str(e)}")
                return False
        
        return True
    
    def print_config_summary(self, config: List[Dict[str, Any]]):
        """打印配置摘要"""
        logger.info("=" * 80)
        logger.info("测试配置摘要:")
        logger.info(f"  数据来源: {'Excel文件' if USE_EXCEL_DATA else '手动配置'}")
        if USE_EXCEL_DATA:
            logger.info(f"  Excel文件: {EXCEL_FILE_NAME}")
        logger.info(f"  总账号数: {len(config)}")
        
        for i, account_config in enumerate(config, 1):
            start_dt = datetime.fromtimestamp(account_config['start_time'])
            end_dt = datetime.fromtimestamp(account_config['end_time'])
            unique_id_short = account_config['unique_id'][:20] + "..." if len(account_config['unique_id']) > 20 else account_config['unique_id']
            
            logger.info(f"  账号 {i}:")
            logger.info(f"    unique_id: {unique_id_short}")
            logger.info(f"    时间范围: {start_dt} 到 {end_dt}")
            logger.info(f"    stop_video_id: {account_config['stop_video_id']}")
        
        logger.info("=" * 80)
    
    async def run_batch_test(self, config: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        运行批量测试
        
        Args:
            config: 测试配置列表
            
        Returns:
            Dict: 测试结果
        """
        logger.info("开始执行批量抓取测试")
        
        # 验证配置
        if not self.validate_config(config):
            return {
                'success': False,
                'message': '配置验证失败',
                'accounts_processed': 0,
                'video_list_total': 0,
                'video_detail_total': 0
            }
        
        # 打印配置摘要
        self.print_config_summary(config)
        
        try:
            # 执行批量抓取
            result = await self.service.batch_crawl_accounts_time_range_data(config)
            
            # 打印结果
            self.print_test_result(result)
            
            return result
            
        except Exception as e:
            error_msg = f"批量测试执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'message': error_msg,
                'accounts_processed': 0,
                'video_list_total': 0,
                'video_detail_total': 0
            }
    
    def print_test_result(self, result: Dict[str, Any]):
        """打印测试结果"""
        logger.info("=" * 80)
        logger.info("批量抓取测试结果:")
        logger.info(f"  处理账号数: {result.get('accounts_processed', 0)}")
        logger.info(f"  视频列表总数: {result.get('video_list_total', 0)}")
        logger.info(f"  视频详情总数: {result.get('video_detail_total', 0)}")
        logger.info(f"  整体消息: {result.get('message', 'N/A')}")
        
        # 打印任务创建信息
        tasks_created = result.get('tasks_created', [])
        if tasks_created:
            logger.info(f"  创建任务数: {len(tasks_created)}")
            logger.info(f"  任务ID列表: {tasks_created}")
        
        # 打印各账号详细结果
        account_results = result.get('account_results', [])
        if account_results:
            logger.info("\n各账号详细结果:")
            success_count = 0
            account_info_success_count = 0
            
            for i, account_result in enumerate(account_results, 1):
                success = account_result.get('success', False)
                if success:
                    success_count += 1
                
                # 检查账号信息抓取状态（如果结果中包含这个信息）
                account_info_crawled = account_result.get('account_info_crawled')
                if account_info_crawled:
                    account_info_success_count += 1
                
                unique_id_short = account_result.get('unique_id', 'N/A')[:30] + "..." if len(account_result.get('unique_id', '')) > 30 else account_result.get('unique_id', 'N/A')
                
                logger.info(f"  账号 {i}: {unique_id_short}")
                logger.info(f"    总体成功: {'✓' if success else '✗'}")
                logger.info(f"    账号ID: {account_result.get('account_id', 'N/A')}")
                logger.info(f"    任务ID: {account_result.get('task_id', 'N/A')}")
                
                # 如果有账号信息抓取状态，显示它
                if account_info_crawled is not None:
                    logger.info(f"    账号信息: {'✓ 抓取成功' if account_info_crawled else '✗ 抓取失败'}")
                
                logger.info(f"    视频列表: {account_result.get('video_list_count', 0)}")
                logger.info(f"    视频详情: {account_result.get('video_detail_count', 0)}")
                logger.info(f"    消息: {account_result.get('message', 'N/A')}")
                logger.info("")
            
            logger.info(f"总体成功率: {success_count}/{len(account_results)} ({success_count/len(account_results)*100:.1f}%)")
            
            # 如果有账号信息抓取统计，显示它
            if any(result.get('account_info_crawled') is not None for result in account_results):
                logger.info(f"账号信息抓取成功率: {account_info_success_count}/{len(account_results)} ({account_info_success_count/len(account_results)*100:.1f}%)")
        
        logger.info("=" * 80)


async def main():
    """主函数"""
    logger.info("开始执行抖音时间范围批量抓取测试（包含账号信息抓取）")
    
    # 获取账号配置
    accounts_config = get_accounts_config()
    
    # 检查配置
    if not accounts_config:
        logger.error("没有可用的账号配置")
        if USE_EXCEL_DATA:
            logger.info(f"请检查Excel文件 {EXCEL_FILE_NAME} 是否存在且包含有效数据")
            logger.info("Excel文件应包含以下列：唯一标识码、开始时间、结束时间、截止视频ID")
        else:
            logger.info("请在 MANUAL_TEST_ACCOUNTS_CONFIG 中配置测试账号")
        
        logger.info("配置格式示例:")
        logger.info("""
MANUAL_TEST_ACCOUNTS_CONFIG = [
    {
        'unique_id': '你的账号unique_id',
        'start_time': datetime_str_to_timestamp("2025-06-20"),           # 只有日期
        'end_time': datetime_str_to_timestamp("2025-06-25 13:19:26"),   # 完整日期时间
        'stop_video_id': '停止视频ID'
    },
]
        """)
        logger.info("\n注意：脚本现在会按照以下顺序执行：")
        logger.info("1. 抓取账号基本信息（昵称、粉丝数、关注数等）")
        logger.info("2. 抓取指定时间范围内的视频列表")
        logger.info("3. 抓取视频详情数据")
        return
    
    try:
        # 创建测试器并运行测试
        tester = DouyinTimeRangeBatchTester()
        result = await tester.run_batch_test(accounts_config)
        
        logger.info("测试执行完成！")
        return result
        
    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}", exc_info=True)
        raise


if __name__ == '__main__':
    asyncio.run(main()) 