import os
import random



# 抖音爬虫配置
DOUYIN_CONFIG = {
    'user_data_dirs': [
        # 登录文件夹, 仅限于抓取阶段历史数据时使用
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'crawl_data/browser_data/douyin_user_0'),
        # 未登录文件件, 普通抓取时使用
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'crawl_data/browser_data/douyin_user_1'),
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'crawl_data/browser_data/douyin_user_2'),
    ],
    'headless': True,  # 有头模式 or 无头模式
    'timeout': 50 * 1000,  # 页面加载超时时间（毫秒）
    'retry_count': 2,  # 重试次数
    'scroll_distance': random.randint(300, 800),  # 滚动距离（像素）
    'scroll_interval': random.randint(1000, 3000),  # 滚动时间间隔（毫秒）
    'history_video_count': 100,  # 历史视频抓取数量
    'update_video_count': 30,  # 增量更新视频数量
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36'
}

# 数据更新配置
DATA_UPDATE_CONFIG = {
    'update_interval_minutes': 3 * 60,  # 数据更新间隔（分钟），小于30建议仅用于测试
    'use_hybrid_interval_checker': True,   # 是否使用混合间隔检查器（同时考虑日期和配置的分钟间隔）
    'update_video_days': 8, # 多少天以内的视频需要被更新详细数据
    # 视频详情重试配置
    'video_detail_retry': {
        'max_retries': 3,          # 视频详情抓取失败后的最大重试次数
        'retry_interval_minutes': 30 # 每次重试的间隔时间（分钟）
    }
}

# 页面版本配置（按页面类型分别管理）
PAGE_VERSION_CONFIG = {
    'enabled': True,  # 启用版本检测，为将来扩展预留
    'detection_timeout': 5000,  # 版本检测超时时间（毫秒）
    # 用户主页版本配置（包含账号信息和视频列表）
    'user_page': {
        'version_priority': ['v1'],  # 目前只有v1版本
    },
    # 视频详情页版本配置
    'video_detail_page': {
        'version_priority': ['v1'],  # 目前只有v1版本
    },
    # 笔记详情页版本配置
    'note_detail_page': {
        'version_priority': ['v1'],  # 目前只有v1版本
    }
}

# 用户主页选择器配置（包含账号信息和视频列表）
USER_PAGE_SELECTORS_V1 = {
    # 账号信息相关选择器
    'account': {
        'nickname': 'div[data-e2e="user-info"] div h1 span span span',  # 博主昵称
        'bio': 'div[data-e2e="user-info"] div:nth-of-type(3) span span span span span span',  # 个人简介
        'account_id': 'div[data-e2e="user-info"] p span',  # 抖音号
        'followed_count': 'div[data-e2e="user-info-follow"] div:nth-of-type(2)',  # 关注数
        'fans_count': 'div[data-e2e="user-info-fans"] div:nth-of-type(2)',  # 粉丝数
        'total_like_count': 'div[data-e2e="user-info-like"] div:nth-of-type(2)',  # 获赞数
        'authenticated_info': 'span[data-e2e="badge-role-name"]',  # 认证信息
        'video_count': 'span[data-e2e="user-tab-count"]',  # 作品数
        'avatar': 'div[data-e2e="user-detail"] span[data-e2e="live-avatar"] img',  # 头像
    },
    # 视频列表相关选择器
    'video_list': {
        'container': 'ul[data-e2e="scroll-list"] li',  # 视频列表容器
        'title': 'div a p', # 视频标题
    }
}

# 视频详情页选择器配置
VIDEO_DETAIL_PAGE_SELECTORS_V1 = {
    'video_detail': {
        'issue_time': 'span[data-e2e="detail-video-publish-time"]',  # 发布时间
        'like_count': 'div[data-e2e="detail-video-info"] > div:nth-of-type(2) > div > div > span',  # 点赞数
        'comment_count': 'div[data-e2e="detail-video-info"] > div:nth-of-type(2) > div > div:nth-of-type(2) > span',  # 评论数
        'collect_count': 'div[data-e2e="detail-video-info"] > div:nth-of-type(2) > div > div:nth-of-type(3) > span',  # 收藏数
        'share_count': 'div[data-e2e="detail-video-info"] > div:nth-of-type(2) > div > div:nth-of-type(4) > span',  # 转发数
    }
}

# 笔记详情页选择器配置
NOTE_DETAIL_PAGE_SELECTORS_V1 = {
    'note_detail': {
        'like_count': 'div[data-e2e="video-player-digg"] div:nth-of-type(2)',  # 点赞数
        'comment_count': 'div[data-e2e="feed-comment-icon"] div:nth-of-type(2)',  # 评论数
        'collect_count': 'div[data-e2e="video-player-collect"] div:nth-of-type(2)',  # 收藏数
        'share_count': 'div[data-e2e="video-player-share"] div:nth-of-type(2)',  # 转发数
        'issue_time': 'main[data-e2e="note-detail"] > div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(1) > div:nth-of-type(2) > span:nth-of-type(1)',  # 发布时间
    }
}

# 向后兼容的统一选择器配置（保持原有接口）
SELECTORS = {
    'account': USER_PAGE_SELECTORS_V1['account'],
    'video_list': USER_PAGE_SELECTORS_V1['video_list'],
    'video_detail': VIDEO_DETAIL_PAGE_SELECTORS_V1['video_detail'],
    'note_detail': NOTE_DETAIL_PAGE_SELECTORS_V1['note_detail'],
}

# 按页面类型和版本组织的选择器映射
PAGE_SELECTORS = {
    'user_page': {
        'v1': USER_PAGE_SELECTORS_V1,
        # 'v2': USER_PAGE_SELECTORS_V2,  # 将来添加新版本时使用
    },
    'video_detail_page': {
        'v1': VIDEO_DETAIL_PAGE_SELECTORS_V1,
        # 'v2': VIDEO_DETAIL_PAGE_SELECTORS_V2,  # 将来添加新版本时使用
    },
    'note_detail_page': {
        'v1': NOTE_DETAIL_PAGE_SELECTORS_V1,
        # 'v2': NOTE_DETAIL_PAGE_SELECTORS_V2,  # 将来添加新版本时使用
    }
}

def get_selectors_for_version(version: str | None = None, page_type: str = 'user_page') -> dict:
    """
    获取指定版本的选择器配置
    
    Args:
        version: 页面版本，如果为None则使用默认版本v1
        page_type: 页面类型 ('user_page', 'video_detail_page', 'note_detail_page')
        
    Returns:
        dict: 对应版本的选择器配置
    """
    # 简化逻辑：如果未指定版本，则使用v1版本
    target_version = version if version else 'v1'
    
    # 获取对应页面类型的选择器
    page_selectors = PAGE_SELECTORS.get(page_type, PAGE_SELECTORS['user_page'])
    
    # 获取对应版本的选择器，如果版本不存在则使用v1版本
    return page_selectors.get(target_version, page_selectors['v1'])